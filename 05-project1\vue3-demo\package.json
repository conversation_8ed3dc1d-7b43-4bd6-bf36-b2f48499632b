{"name": "vue3-demo", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/", "prepare": "husky install", "lint-staged": "lint-staged"}, "dependencies": {"axios": "^1.8.1", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.20.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.20.1", "eslint-plugin-vue": "^9.32.0", "husky": "^8.0.0", "lint-staged": "^15.4.3", "prettier": "^3.5.1", "vite": "^6.1.0", "vite-plugin-vue-devtools": "^7.7.2"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix"]}}