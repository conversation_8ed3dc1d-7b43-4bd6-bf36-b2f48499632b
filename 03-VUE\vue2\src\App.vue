<template>
  <div id="app">
    <table>
      <thead>
        <tr>
        <th>商品图片</th>
        <th>商品详情</th>
        <th>商品信息</th>
        <th>操作</th>
        </tr>
      </thead>
      <MyList v-for="item in list" :key="item.id" :item="item" @change="changeHandle"></MyList>
    </table>
  </div>
</template>

<script>
import MyList from '@/components/MyList.vue'
import { getList, setList } from '@/api/list'
export default {
  data () {
    return {
      list: [
      ]
    }
  },
  methods: {
    async changeHandle (item) {
      const res = await setList(item)
      console.log(res)
    }
  },
  components: {
    MyList
  },
  async created () {
    const res = await getList()
    this.list = res.data.data.pageData.items
  }
}

</script>

<style lang="less" scoped>
table{
  margin: 0 auto;
  border-spacing: 0;
  border-collapse: collapse;
  border: 1px solid gray;
  th{
    height: 50px;
    line-height: 50px;
    padding-left: 10px;
    text-align: left;
    border: 1px solid gray;
    width: 100px;
  }
  th:nth-child(2){
    width: 500px;
  }
}
</style>
