<template>
 <tbody>
  <tr>
    <th>
      <img src="" alt="">
    </th>
    <th>{{item.name}}</th>
    <th>{{item.type}}</th>
    <th>
      <button @click="changeList(item)">一键生成</button>
    </th>
  </tr>
 </tbody>
</template>

<script>
export default {
  props: ['item'],
  data () {
    return {

    }
  },
  methods: {
    changeList (item) {
      this.$emit('change', item)
    }
  }
}
</script>

<style lang="less" scoped>
 tbody{
  th{
    border: 1px solid gray;
    img{
      height: 100px;
      width: 100px;
    }
  }
  th:last-child button{
    border: none;
    background-color: #fff;
    color: blue;
  }
  th:last-child button:hover{
    color: red;
  }
 }

</style>
