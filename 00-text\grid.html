<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <style>
      .parent {
        display: grid;
        gap: 10px;
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-rows: 100px 200px 300px;
        /* grid-template-areas:
          "a a b b b b c c c c d d"
          "a a b b b b c c c c d d"
          "e e b b b b c c c c d d"
          "e e f f f f g g g g d d"
          "e e f f f f g g g g d d"
          "e e f f f f g g g g d d"
          "e e f f f f g g g g d d"
          "e e f f f f g g g g d d"
          "e e f f f f g g g g d d"; */
      }
      .box {
        background: red;
      }
    </style>
  </head>
  <body>
    <div class="parent">
      <div class="box box1"></div>
      <div class="box box2"></div>
      <div class="box box3"></div>
      <div class="box box4"></div>
      <div class="box box5"></div>
      <div class="box box6"></div>
      <div class="box box7"></div>
      <div class="box box8"></div>
      <div class="box box9"></div>
      <div class="box box10"></div>
      <div class="box box11"></div>
      <div class="box box12"></div>
      <div class="box box13"></div>
      <div class="box box14"></div>
    </div>
  </body>
</html>
