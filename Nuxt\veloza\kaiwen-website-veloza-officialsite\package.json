{"name": "velose-vite", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/fonts": "^0.10.2", "@nuxtjs/i18n": "^8.0.0-beta.13", "@pinia/nuxt": "^0.7.0", "@splinetool/viewer": "^1.9.46", "nuxt": "^3.14.159", "nuxt-swiper": "^2.0.0", "pinia": "^2.2.6", "sass": "^1.80.6", "sass-loader": "^13.1.0", "vue": "latest", "vue-router": "latest", "vue3-google-map": "^0.21.0"}, "devDependencies": {"@element-plus/nuxt": "^1.1.0", "@nuxtjs/tailwindcss": "^6.12.2", "element-plus": "^2.8.7", "typescript": "^5.6.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}